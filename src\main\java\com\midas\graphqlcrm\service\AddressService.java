package com.midas.graphqlcrm.service;

import com.midas.graphqlcrm.entity.Address;
import com.midas.graphqlcrm.repository.AddressRepository;
import com.midas.graphqlcrm.util.MapperUtil;
import com.zee.graphqlcrm.codegen.types.AddressDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class AddressService {

    private final AddressRepository addressRepository;
    private final MapperUtil mapperUtil;

    // Buscar lista de direcciones por entityId
    public List<AddressDto> findAddressByEntityId(String entityId) {
        return addressRepository.findByEntityId(entityId)
                .stream()
                .map(mapperUtil::mapToAddressDto)
                .toList();
    }

    // Buscar una dirección específica por entityId y uuid
    public Optional<AddressDto> findAddressByEntityIdAndUuid(String entityId, UUID uuid) {
        return addressRepository.findByEntityIdAndUuid(entityId, uuid)
                .map(mapperUtil::mapToAddressDto);
    }

    // Guardar múltiples direcciones
    public void saveAll(List<Address> addresses) {
        addressRepository.saveAll(addresses);
    }

    // Guardar una sola dirección
    public Address save(Address address) {
        return addressRepository.save(address);
    }
}

