type CompanyDto {
    uuid: String!
    name: String!,
    rcNumber: String!,
    headOffice: String!,
    country: String!,
    businessType: BusinessType!
}

enum BusinessType{
    TELECOMS, BANKING, EDUCATION
}

input CompanyInput {
    name: String!
    rcNumber: String!
    headOffice: String!
    country: String!
    businessType: BusinessType!
}

type ALLCompanyResponse implements Response {
    success: Boolean!
    message: String
    companies: [CompanyDto!]
}
