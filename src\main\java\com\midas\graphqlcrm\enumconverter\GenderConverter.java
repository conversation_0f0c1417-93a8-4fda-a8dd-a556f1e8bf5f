package com.midas.graphqlcrm.enumconverter;

import com.zee.graphqlcrm.codegen.types.Gender;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.Arrays;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @created : 06 Oct, 2024
 */
@Converter(autoApply = true)
public class GenderConverter implements AttributeConverter<Gender, String> {

    @Override
    public String convertToDatabaseColumn(Gender attribute) {
        return attribute.name(); // Guarda el nombre del enum como String
    }

    @Override
    public Gender convertToEntityAttribute(String dbData) {
        return Arrays.stream(Gender.values())
                .filter(gen -> gen.name().equalsIgnoreCase(dbData))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}

