package com.midas.graphqlcrm.enumconverter;

import com.zee.graphqlcrm.codegen.types.Role;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.Arrays;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @created : 06 Oct, 2024
 */
@Converter(autoApply = true)
public class RoleConverter implements AttributeConverter<Role, String> {

    @Override
    public String convertToDatabaseColumn(Role attribute) {
        return attribute.name();
    }

    @Override
    public Role convertToEntityAttribute(String dbData) {
        return Arrays.stream(Role.values())
                .filter(role -> role.name().equalsIgnoreCase(dbData))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}

