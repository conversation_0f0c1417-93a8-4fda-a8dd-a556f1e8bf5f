package com.midas.graphqlcrm.api;

import com.midas.graphqlcrm.service.CompanyService;
import com.zee.graphqlcrm.codegen.DgsConstants;
import com.zee.graphqlcrm.codegen.types.CompanyInput;
import com.zee.graphqlcrm.codegen.types.CreationResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.stereotype.Controller;

@Controller
@RequiredArgsConstructor
public class CompanyController {

    private final CompanyService companyService;

    @SchemaMapping(
            typeName = DgsConstants.MUTATION.TYPE_NAME,
            field = DgsConstants.MUTATION.CreateCompany
    )
    public CreationResponse createCompany(@Argument(value = "companyInput") CompanyInput input) {
        return companyService.createCompany(input);
    }
}
