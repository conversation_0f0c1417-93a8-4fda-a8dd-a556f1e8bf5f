type Sample {
    id: ID!             # this means compulsory
    name: String!
    age: Int,
    height: Float,
    isTall: Boolean
}

interface Animal {
    legs: Int!
    hasFeather: Boolean
    color: Color!
}

type <PERSON> implements Animal {
    legs: Int!
    hasFeather: Boolean
    color: Color!
    isBark: Boolean
}

type <PERSON> implements Animal {
    legs: Int!
    hasFeather: Boolean
    color: Color!
    wiggleButt: Boolean!
}

type Lion implements Animal {
    legs: Int!
    hasFeather: Boolean
    color: Color!
    roar: RoarIntensity
}

enum Color {
    BROWN
    TAN
    DARK_BROWN
    AMBER
}

enum RoarIntensity {
    QUIET
    MODERATE
    LOUD
    DEAFENING
}

input FetchSampleInput {
    id: ID!
    name: String!
}

type SampleResponse {
    id: ID!
    name: String
}

# all the reads you do in Http
# R in CRUD
# getOne, readUser
# a square bracket means a list
type SampleQuery {
    fetchSample(fetchSampleInput: FetchSampleInput, input2: BigDecimal): [Sample!]
    fetchSample2(sample: BigDecimal): [Sample!]
    fetchSample3: [Sample!]!
}

# all data changes you do in Http
# C-create, U-update, D-delete in CRUD
type SampleMutation {
    createSample(createInput: FetchSampleInput): SampleResponse
}

# publisher-subscriber model
type Subscription {
    createSampleNotification: Sample
}
