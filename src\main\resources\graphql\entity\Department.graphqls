type DepartmentDto{
    uuid: String!,
    name: String!,
    departmentNo: String!,
    companyName: String!,
    rcNumber: String!,
    address: AddressDto,
    division: Division!
}

enum Division {
    HR, SALES, TECH, CUSTOMER_SERVICE
}

input DepartmentInput {
    name: String!
    departmentNo: String!
    companyName: String!
    rcNumber: String!
    address: AddressInput!
    division: Division!
}

type DepartmentsResponse implements Response {
    success: Boolean!
    message: String
    companies: [DepartmentDto!]
}
