package com.midas.graphqlcrm.util;

import com.midas.graphqlcrm.entity.*;
import com.zee.graphqlcrm.codegen.types.*;
import org.springframework.stereotype.Component;

@Component
public class MapperUtil {

    public Company mapToCompanyEntity(final CompanyInput input) {
        Company company = new Company();

        company.setName(input.getName());
        company.setRcNumber(input.getRcNumber());
        company.setHeadOffice(input.getHeadOffice());
        company.setCountry(input.getCountry());
        company.setBusinessType(input.getBusinessType());

        return company;
    }

    public CompanyDto mapToCompanyDto(final Company company) {
        CompanyDto companyDto = new CompanyDto();

        companyDto.setUuid(company.getUuid().toString());
        companyDto.setName(company.getName());
        companyDto.setRcNumber(company.getRcNumber());
        companyDto.setHeadOffice(company.getHeadOffice());
        companyDto.setCountry(company.getCountry());
        companyDto.setBusinessType(company.getBusinessType());

        return companyDto;
    }

    public Department mapToDepartmentEntity(final DepartmentInput input) {
        Department department = new Department();

        department.setName(input.getName());
        department.setRcNumber(input.getRcNumber());
        department.setDepartmentNo(input.getDepartmentNo());
        department.setCompanyName(input.getCompanyName());
        department.setDivision(input.getDivision());

        return department;
    }

    public DepartmentDto mapToDepartmentDto(final Department department) {
        DepartmentDto departmentDto = new DepartmentDto();

        departmentDto.setUuid(department.getUuid().toString());
        departmentDto.setName(department.getName());
        departmentDto.setDepartmentNo(department.getDepartmentNo());
        departmentDto.setCompanyName(department.getCompanyName());
        departmentDto.setRcNumber(department.getRcNumber());
        departmentDto.setDivision(department.getDivision());

        return departmentDto;
    }

    public Address mapToAddressEntity(final AddressInput input) {
        Address address = new Address();

        address.setEntityId(input.getEntityId());
        address.setStreet(input.getStreet());
        address.setCity(input.getCity());
        address.setState(input.getState());
        address.setZipCode(input.getZipCode());

        return address;
    }

    public AddressDto mapToAddressDto(final Address address) {
        AddressDto addressDto = new AddressDto();

        addressDto.setUuid(address.getUuid().toString());
        addressDto.setEntityId(address.getEntityId());
        addressDto.setStreet(address.getStreet());
        addressDto.setCity(address.getCity());
        addressDto.setState(address.getState());
        addressDto.setZipCode(address.getZipCode());

        return addressDto;
    }

    // Convierte EmployeeOutsourcedInput directamente a Employee
    public Employee mapToEmployeeEntity(final EmployeeOutsourcedInput input) {
        Employee employee = new Employee();

        employee.setName(input.getName());
        employee.setDateOfBirth(input.getDateOfBirth());
        employee.setGender(input.getGender());
        employee.setSalary(input.getSalary());
        employee.setAge(input.getAge());
        employee.setPhone(input.getPhone());
        employee.setCompanyName(input.getCompanyName());
        employee.setEmployeeId(input.getEmployeeId());
        employee.setDepartmentNo(input.getDepartmentNo());
        employee.setEmail(input.getEmail());
        employee.setRole(input.getRole());

        return employee;
    }

    public EmployeeDto mapToEmployeeDto(final Employee employee) {
        EmployeeDto dto = new EmployeeDto();

        dto.setUuid(employee.getUuid().toString());
        dto.setName(employee.getName());
        dto.setDateOfBirth(employee.getDateOfBirth());
        dto.setGender(employee.getGender());
        dto.setSalary(employee.getSalary());
        dto.setAge(employee.getAge());
        dto.setPhone(employee.getPhone());
        dto.setCompanyName(employee.getCompanyName());
        dto.setEmployeeId(employee.getEmployeeId());
        dto.setDepartmentNo(employee.getDepartmentNo());
        dto.setEmail(employee.getEmail());
        dto.setRole(employee.getRole());

        return dto;
    }

    public Outsourced mapToOutsourcedEntity(final EmployeeOutsourcedInput input) {
        Outsourced outsourced = new Outsourced();

        outsourced.setName(input.getName());
        outsourced.setDateOfBirth(input.getDateOfBirth());
        outsourced.setGender(input.getGender());
        outsourced.setSalary(input.getSalary());
        outsourced.setAge(input.getAge());
        outsourced.setPhone(input.getPhone());
        outsourced.setCompanyName(input.getCompanyName());
        outsourced.setOutsourceId(input.getOutsourceId());
        outsourced.setDuty(input.getDuty());

        return outsourced;
    }

    public OutsourcedDto mapToOutsourcedDto(final Outsourced outsourced) {
        OutsourcedDto dto = new OutsourcedDto();

        dto.setUuid(outsourced.getUuid().toString());
        dto.setName(outsourced.getName());
        dto.setDateOfBirth(outsourced.getDateOfBirth());
        dto.setGender(outsourced.getGender());
        dto.setSalary(outsourced.getSalary());
        dto.setAge(outsourced.getAge());
        dto.setPhone(outsourced.getPhone());
        dto.setCompanyName(outsourced.getCompanyName());
        dto.setOutsourceId(outsourced.getOutsourceId());
        dto.setDuty(outsourced.getDuty());

        return dto;
    }

}
