package com.midas.graphqlcrm.enumconverter;

import com.zee.graphqlcrm.codegen.types.BusinessType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

import java.util.Arrays;

@Converter(autoApply = true)
public class BusinessTypeConverter implements AttributeConverter<BusinessType, String> {

    @Override
    public String convertToDatabaseColumn(BusinessType attribute) {
        return attribute.name(); // Convierte el enum a su nombre en texto
    }

    @Override
    public BusinessType convertToEntityAttribute(String dbData) {
        return Arrays.stream(BusinessType.values())
                .filter(type -> type.name().equalsIgnoreCase(dbData))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}