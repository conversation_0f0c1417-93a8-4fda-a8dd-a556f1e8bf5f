package com.midas.graphqlcrm.enumconverter;

import com.zee.graphqlcrm.codegen.types.Duty;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.Arrays;

@Converter(autoApply = true)
public class DutyConverter implements AttributeConverter<Duty, String> {

    @Override
    public String convertToDatabaseColumn(Duty attribute) {
        return attribute.toString(); // O attribute.name() si Duty es un enum
    }

    @Override
    public Duty convertToEntityAttribute(String dbData) {
        return Arrays.stream(Duty.values())
                .filter(duty -> duty.name().equalsIgnoreCase(dbData))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}

