package com.midas.graphqlcrm.api;

import com.midas.graphqlcrm.service.EmployeeOutsourcedService;
import com.zee.graphqlcrm.codegen.DgsConstants;
import com.zee.graphqlcrm.codegen.types.AddressInput;
import com.zee.graphqlcrm.codegen.types.CreationResponse;
import com.zee.graphqlcrm.codegen.types.EmployeeOutsourcedInput;
import com.zee.graphqlcrm.codegen.types.EmployeeUpdateInput;
import lombok.RequiredArgsConstructor;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.stereotype.Controller;

@Controller
@RequiredArgsConstructor
public class EmployeeController {

    private final EmployeeOutsourcedService service;

    @SchemaMapping(
            typeName = DgsConstants.MUTATION.TYPE_NAME,
            field = DgsConstants.MUTATION.CreateEmployee
    )
    public CreationResponse createEmployee(@Argument(value = "employeeOutsourced") EmployeeOutsourcedInput input) {
        return service.createEmployeeOutsourced(input);
    }

    @SchemaMapping(
            typeName = DgsConstants.MUTATION.TYPE_NAME,
            field = DgsConstants.MUTATION.UpdateExistingEmployeeAddress
    )
    public CreationResponse updateExistingEmployeeAddress(@Argument(value = "addressInput") AddressInput input) {
        return service.updateExistingEmployeeAddress(input);
    }

    @SchemaMapping(
            typeName = DgsConstants.MUTATION.TYPE_NAME,
            field = DgsConstants.MUTATION.UpdateEmployeeDetails
    )
    public CreationResponse updateEmployeeDetails(@Argument(value = "employeeUpdate") EmployeeUpdateInput input) {
        return service.updateEmployeeDetails(input);
    }
}

