package com.midas.graphqlcrm.service;

import com.midas.graphqlcrm.entity.Address;
import com.midas.graphqlcrm.entity.Employee;
import com.midas.graphqlcrm.entity.Outsourced;
import com.midas.graphqlcrm.repository.EmployeeRepository;
import com.midas.graphqlcrm.repository.OutsourcedRepository;
import com.midas.graphqlcrm.util.MapperUtil;
import com.zee.graphqlcrm.codegen.types.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class EmployeeOutsourcedService {

    private final EmployeeRepository employeeRepository;
    private final OutsourcedRepository outsourcedRepository;
    private final MapperUtil mapperUtil;
    private final AddressService addressService;

    public CreationResponse createEmployeeOutsourced(EmployeeOutsourcedInput input) {
        return (!StringUtils.hasText(input.getOutsourceId()) && input.getDuty() == null)
                ? createEmployee(input) : createOutsourced(input);

    }

    public CreationResponse createEmployee(EmployeeOutsourcedInput input) {
        if (!StringUtils.hasText(input.getEmployeeId()) ||
                !StringUtils.hasText(input.getDepartmentNo()) ||
                !StringUtils.hasText(input.getEmail()) ||
                input.getRole() == null) {
            throw new RuntimeException("employeeId, departmentNo, email, role is required");
        }

        // Guardar en tabla employee directamente desde el input
        Employee persistedEmployee = employeeRepository.save(mapperUtil.mapToEmployeeEntity(input));

        // Mapear y guardar direcciones
        List<Address> addressList = input.getAddress().stream()
                .map(addressInput -> {
                    Address address = mapperUtil.mapToAddressEntity(addressInput);
                    address.setEntityId(persistedEmployee.getEmployeeId()); // importante
                    return address;
                })
                .collect(Collectors.toList());

        addressService.saveAll(addressList);

        return CreationResponse.newBuilder()
                .uuid(persistedEmployee.getUuid().toString())
                .message("Employee " + persistedEmployee.getName() + " created successfully")
                .success(true)
                .build();
    }


    public CreationResponse createOutsourced(EmployeeOutsourcedInput input) {
        if (!StringUtils.hasText(input.getOutsourceId()) || input.getDuty() == null) {
            throw new RuntimeException("outsourceId and duty are required");
        }

        // Guardar en tabla outsourced directamente desde el input
        Outsourced persistedOutsourced = outsourcedRepository.save(mapperUtil.mapToOutsourcedEntity(input));

        // Mapear y guardar direcciones
        List<Address> addressList = input.getAddress().stream()
                .map(addressInput -> {
                    Address address = mapperUtil.mapToAddressEntity(addressInput);
                    address.setEntityId(persistedOutsourced.getOutsourceId()); // importante
                    return address;
                })
                .collect(Collectors.toList());

        addressService.saveAll(addressList);

        return CreationResponse.newBuilder()
                .uuid(persistedOutsourced.getUuid().toString())
                .message("Outsourced with name " + persistedOutsourced.getName() + " created successfully")
                .success(true)
                .build();
    }



    public CreationResponse updateExistingEmployeeAddress(AddressInput input) {
        // Validación básica
        if (input.getEntityId() == null || input.getUuid() == null) {
            throw new RuntimeException("EntityId and UUID are required");
        }

        // Buscar dirección actual
        AddressDto foundAddress = addressService
                .findAddressByEntityIdAndUuid(input.getEntityId(), UUID.fromString(input.getUuid()))
                .orElseThrow(() -> new RuntimeException(
                        "Address for employee with id '" + input.getEntityId() + "' not found"
                ));

        // Validar si todos los campos son iguales
        if (input.getEntityId().trim().equalsIgnoreCase(foundAddress.getEntityId())
                && input.getUuid().trim().equalsIgnoreCase(foundAddress.getUuid())
                && input.getStreet().trim().equalsIgnoreCase(foundAddress.getStreet())
                && input.getCity().trim().equalsIgnoreCase(foundAddress.getCity())
                && input.getState().trim().equalsIgnoreCase(foundAddress.getState())
                && input.getZipCode() == foundAddress.getZipCode()) {
            throw new RuntimeException("Address for employee with id '" + input.getEntityId() + "' already exists");
        }

        input.setUuid(foundAddress.getUuid());
        // Mapear input a entidad
        Address newAddress = mapperUtil.mapToAddressEntity(input);
        newAddress.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

        // Guardar actualización
        Address persistedAddress = addressService.save(newAddress);

        // Construir respuesta
        return CreationResponse.newBuilder()
                .uuid(persistedAddress.getUuid().toString())
                .message("Employee Address updated successfully")
                .success(true)
                .build();
    }

    public CreationResponse updateEmployeeDetails(EmployeeUpdateInput input) {
        // Buscar al empleado por ID
        Employee employee = employeeRepository.findByEmployeeId(input.getEmployeeId())
                .orElseThrow(() -> new RuntimeException(
                        "Employee with id '" + input.getEmployeeId() + "' not found"
                ));

        // Actualizar datos
        employee.setSalary(input.getSalary());
        employee.setAge(input.getAge());
        employee.setRole(input.getRole());
        employee.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

        // Guardar empleado actualizado
        Employee updatedEmployee = employeeRepository.save(employee);

        // Respuesta
        return CreationResponse.newBuilder()
                .uuid(updatedEmployee.getUuid().toString())
                .message("Employee details updated successfully")
                .success(true)
                .build();
    }

}
