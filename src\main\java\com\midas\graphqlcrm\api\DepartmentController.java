package com.midas.graphqlcrm.api;

import com.midas.graphqlcrm.service.DepartmentService;
import com.zee.graphqlcrm.codegen.DgsConstants;
import com.zee.graphqlcrm.codegen.types.CreationResponse;
import com.zee.graphqlcrm.codegen.types.DepartmentInput;
import lombok.RequiredArgsConstructor;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.stereotype.Controller;

@Controller
@RequiredArgsConstructor
public class DepartmentController {

    private final DepartmentService departmentService;

    @SchemaMapping(
            typeName = DgsConstants.MUTATION.TYPE_NAME,
            field = DgsConstants.MUTATION.CreateDepartment
    )
    public CreationResponse createDepartment(@Argument(value = "departmentInput") DepartmentInput input) {
        return departmentService.createDepartment(input);
    }
}
