services:
  mysql:
    image: mysql:8.0
    container_name: graphql-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: graphql_course
      MYSQL_USER: root
      MYSQL_PASSWORD: root
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

volumes:
  mysql_data:
