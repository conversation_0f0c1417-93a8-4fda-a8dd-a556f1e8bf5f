package com.midas.graphqlcrm.repository;

import com.midas.graphqlcrm.entity.Address;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


@Repository
public interface AddressRepository extends JpaRepository<Address, UUID>, JpaSpecificationExecutor<Address> {

    List<Address> findByEntityId(String entityId);

    Optional<Address> findByEntityIdAndUuid(String addressId, UUID uuid);
}
