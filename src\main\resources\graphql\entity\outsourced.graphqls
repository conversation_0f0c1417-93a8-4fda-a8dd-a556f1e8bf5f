type OutsourcedDto implements Person {
    uuid: String!
    name: String!
    dateOfBirth: Date!
    gender: Gender!
    salary: BigDecimal!
    address: [AddressDto!]
    age: PositiveInt
    phone: String!
    companyName: String
    active: Boolean!

    outsourceId: String!
    duty: Duty!
}

enum Duty {
    JANITOR
    ELECTRICIAN
    SWEEPER
}

input OutsourcesInput {
    name: String!
    dateOfBirth: Date!
    gender: Gender!
    salary: BigDecimal!
    address: [AddressInput]
    age: PositiveInt
    phone: PositiveInt

    outsourceId: String!
    duty: Duty!
}
