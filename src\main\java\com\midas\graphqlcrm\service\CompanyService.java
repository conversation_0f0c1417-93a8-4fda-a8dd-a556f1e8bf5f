package com.midas.graphqlcrm.service;

import com.midas.graphqlcrm.entity.Company;
import com.midas.graphqlcrm.repository.CompanyRepository;
import com.midas.graphqlcrm.util.MapperUtil;
import com.zee.graphqlcrm.codegen.types.CompanyInput;
import com.zee.graphqlcrm.codegen.types.CreationResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompanyService {

    private final CompanyRepository companyRepository;
    private final MapperUtil mapperUtil;

    public CreationResponse createCompany(CompanyInput input) {
        Company persistedCompany = companyRepository.save(
                mapperUtil.mapToCompanyEntity(input)
        );

        return CreationResponse.newBuilder()
                .uuid(persistedCompany.getUuid().toString())
                .message("Company with name " + persistedCompany.getName() + " created successfully")
                .success(true)
                .build();
    }
}

