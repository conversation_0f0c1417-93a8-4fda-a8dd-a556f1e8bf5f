type EmployeeDto implements Person {
    uuid: String!
    name: String!
    dateOfBirth: Date!
    gender: Gender!
    salary: BigDecimal!
    address: [AddressDto!]
    age: PositiveInt
    phone: String!
    companyName: String
    active: Boolean!

    employeeId: String!
    departmentNo: String!
    email: String!
    role: Role
}

enum Role {
    INTERN
    SUPERVISOR
    MANAGER
    GMD
}

input EmployeeInput {
    name: String!
    dateOfBirth: Date!
    gender: Gender!
    salary: BigDecimal!
    address: [AddressInput]
    age: PositiveInt
    phone: String!

    employeeId: String!
    departmentNo: String!
    email: String!
    role: Role!
}

input EmployeeUpdateInput {
    employeeId: String!
    salary: BigDecimal!
    age: PositiveInt
    role: Role!
}
