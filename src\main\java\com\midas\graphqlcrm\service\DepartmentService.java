package com.midas.graphqlcrm.service;

import com.midas.graphqlcrm.entity.Address;
import com.midas.graphqlcrm.entity.Department;
import com.midas.graphqlcrm.repository.DepartmentRepository;
import com.midas.graphqlcrm.util.MapperUtil;
import com.zee.graphqlcrm.codegen.types.CreationResponse;
import com.zee.graphqlcrm.codegen.types.DepartmentInput;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DepartmentService {

    private final DepartmentRepository departmentRepository;
    private final MapperUtil mapperUtil;
    private final AddressService addressService;

    public CreationResponse createDepartment(DepartmentInput input) {
        Department persistedDept = departmentRepository.save(
                mapperUtil.mapToDepartmentEntity(input)
        );

        Address address = mapperUtil.mapToAddressEntity(input.getAddress());
        address.setEntityId(persistedDept.getDepartmentNo());

        addressService.save(address);

        return CreationResponse.newBuilder()
                .uuid(persistedDept.getUuid().toString())
                .message("Department with name " + persistedDept.getName() + " created successfully")
                .success(true)
                .build();
    }
}

