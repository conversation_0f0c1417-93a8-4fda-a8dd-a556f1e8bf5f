package com.midas.graphqlcrm.enumconverter;

import com.zee.graphqlcrm.codegen.types.Division;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.Arrays;

@Converter(autoApply = true)
public class DivisionConverter implements AttributeConverter<Division, String> {

    @Override
    public String convertToDatabaseColumn(Division attribute) {
        return attribute.name(); // Guarda como texto (por ejemplo: "SALES")
    }

    @Override
    public Division convertToEntityAttribute(String dbData) {
        return Arrays.stream(Division.values())
                .filter(div -> div.name().equalsIgnoreCase(dbData))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
